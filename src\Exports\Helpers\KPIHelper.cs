using System.Collections.Generic;

namespace Exports.Helpers
{
    public static class KPIHelper
    {
        private List<KpiTemplate> GetCapTableKpiList(int companyId, List<int> moduleIds)
        {
            return (from mapping in _unitOfWork.MappingCapTableRepository.GetQueryable()
                    join master in _unitOfWork.MCapTableRepository.GetQueryable() on mapping.KpiId equals master.KpiId
                    where mapping.PortfolioCompanyId == companyId && !mapping.IsDeleted && !master.IsDeleted && moduleIds.Contains(mapping.ModuleId)
                    select new KpiTemplate
                    {
                        LineItem = master.Kpi,
                        Id = master.KpiId,
                        DisplayOrder = mapping.DisplayOrder,
                        IsHeader = mapping.ParentKpiId == null || mapping.ParentKpiId == 0 ? true : master.IsHeader,
                        KpiTypeId = master.KpiTypeId ?? 0,
                        ModuleId = mapping.ModuleId
                    }).OrderBy(x => x.DisplayOrder).ToList();
        }
    }
}