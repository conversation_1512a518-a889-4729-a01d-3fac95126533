using Contract.PortfolioCompany;
using Exports.Models;
using System;
using System.Collections.Generic;
using XLHelper;
using XLHelper.Configurations;
using XLHelper.Models;

namespace Exports.Helpers
{
    public static class CompanyHeaderHelper
    {
        public static List<Cell> CreateSheetHeader(CompanyExcelModel company, Func<FontStyleOption> GetFontBoldStyle,List<FillStyleOption> bgColorList)
        {
            List<Cell> headerCells = new();
            var cell1 = GetCell(company?.CompanyName, GetFontBoldStyle, bgColorList[0], 14, XLHelper.Constant.Constants.WhiteColor, 1, 1);
            headerCells.Add(cell1);
            var cell2 = GetCell("Currency", GetFontBoldStyle, bgColorList[1], 12, XLHelper.Constant.Constants.WhiteColor, 2, 1);
            headerCells.Add(cell2);
            var cell6 = GetCell(company?.CurrencyCode, GetFontBoldStyle, bgColorList[2], 12, XLHelper.Constant.Constants.Black, 2, 2);
            headerCells.Add(cell6);
            var cell5 = GetCell(company?.Units, GetFontBoldStyle, bgColorList[2], 12, XLHelper.Constant.Constants.Black, 3, 2);
            headerCells.Add(cell5);
            var cell4 = GetCell("Unit", GetFontBoldStyle, bgColorList[1], 12, XLHelper.Constant.Constants.WhiteColor, 3, 1);
            headerCells.Add(cell4);
            var cell3 = GetCell("KPI", GetFontBoldStyle, bgColorList[0], 12, XLHelper.Constant.Constants.WhiteColor, 5, 1);
            cell3.Style.Alignment = new AlignmentStyleOption() { Horizontal = AlignmentHorizontalValues.Center };
            headerCells.Add(cell3);
            return headerCells;
        }
        private static Cell GetCell(string value, Func<FontStyleOption> GetFontBoldStyle, FillStyleOption BackgroundColor, int fontSize, string fontColor, int row, int col)
        {
            var cell = new Cell(row, col, value);
            cell.Style.Font = GetFontBoldStyle();
            cell.Style.Font.Size = fontSize;
            cell.Style.Fill = BackgroundColor;
            cell.Style.Font.Color = fontColor;
            return cell;
        }
        public static ExcelStyleOption GetStyleList()
        {
            Func<FontStyleOption> GetFontBoldStyle = ExcelFileHelper.ApplyBoldStyle();
            var bgColorList = new List<FillStyleOption>
            {
                ExcelFileHelper.ApplyBgColor(XLHelper.Constant.Constants.DarkBlue),
                ExcelFileHelper.ApplyBgColor(XLHelper.Constant.Constants.VioletColor),
                ExcelFileHelper.ApplyBgColor(XLHelper.Constant.Constants.BeatLightBlue)
            };
            return new ExcelStyleOption()
            {
                FontStyles = GetFontBoldStyle,
                AlignmentStyleOption = ExcelFileHelper.ApplyIndent(1),
                FillStyleOptions = bgColorList
            };
        }
    }
}
