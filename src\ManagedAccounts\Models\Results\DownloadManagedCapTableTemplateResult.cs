using System;

namespace ManagedAccounts.Models.Results
{
    /// <summary>
    /// Result for downloading managed account cap table template
    /// </summary>
    public class DownloadManagedCapTableTemplateResult
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// The file path of the generated template
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// The file name for download
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Creates a successful result
        /// </summary>
        /// <param name="filePath">The file path</param>
        /// <param name="fileName">The file name</param>
        /// <returns>Success result</returns>
        public static DownloadManagedCapTableTemplateResult Success(string filePath, string fileName)
        {
            return new DownloadManagedCapTableTemplateResult
            {
                IsSuccess = true,
                FilePath = filePath,
                FileName = fileName
            };
        }

        /// <summary>
        /// Creates a failure result
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        /// <returns>Failure result</returns>
        public static DownloadManagedCapTableTemplateResult Failure(string errorMessage)
        {
            return new DownloadManagedCapTableTemplateResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }
}
