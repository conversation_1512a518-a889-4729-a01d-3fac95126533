using System;
using ManagedAccounts.Models.Results;
using MediatR;

namespace ManagedAccounts.Models.Queries
{
    /// <summary>
    /// Query for downloading managed account cap table template
    /// </summary>
    public class DownloadManagedCapTableTemplateQuery : IRequest<DownloadManagedCapTableTemplateResult>
    {
        /// <summary>
        /// The ID of the managed account
        /// </summary>
        public Guid ManagedAccountId { get; set; }

        /// <summary>
        /// The module ID for KPI filtering
        /// </summary>
        public int ModuleId { get; set; }

        /// <summary>
        /// The user ID for audit purposes
        /// </summary>
        public int UserId { get; set; }
    }
}
