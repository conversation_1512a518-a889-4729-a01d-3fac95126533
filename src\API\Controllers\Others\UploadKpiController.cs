using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.Account;
using Contract.BulkUpload;
using Contract.Utility;
using Exports.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared;
using System;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Utility.Helpers;

namespace API.Controllers.Others;

/// <summary>
/// Controller for handling KPI upload operations and template generation
/// </summary>
[Route("api")]
[ApiController]
[Authorize(Policy = JwtBearerDefaults.AuthenticationScheme)]
public class UploadKpiController : ControllerBase
{
    private readonly IConfigHeaderService _configHeaderService;
    private readonly IEncryption _encryption;
    private readonly IHelperService _helperService;

    /// <summary>
    /// Initializes a new instance of the <see cref="UploadKpiController"/> class
    /// </summary>
    /// <param name="configHeaderService">Service for configuring excel headers</param>
    /// <param name="encryption">Service for encryption/decryption operations</param>
    /// <param name="helperService">Helper service for common operations</param>
    public UploadKpiController(IConfigHeaderService configHeaderService, IEncryption encryption, IHelperService helperService)
    {
        _configHeaderService = configHeaderService;
        _encryption = encryption;
        _helperService = helperService;
    }

    /// <summary>
    /// Downloads an Excel template for KPI data import based on the specified module type
    /// </summary>
    /// <param name="filter">Filter containing module type and portfolio company information</param>
    /// <returns>An Excel file containing the appropriate template for the specified module</returns>
    /// <remarks>
    /// Supports templates for:
    /// - Cap Table
    /// - KPI Financials
    /// - Other KPI types
    /// </remarks>
    [HttpPost]
    [UserFeatureAuthorize((int)Features.BulkUpload, (int)Features.AccessAndWorkflow, (int)Features.Cashflow)]
    [Route("bulk-upload/Kpi/import/template")]
    public async Task<IActionResult> DownloadKPIExcelTemplate([FromBody] BulkUploadTemplateFilter filter)
    {
        if (filter == null || string.IsNullOrWhiteSpace(filter.ModuleType))
        {
            return BadRequest("Invalid module type");
        }

        // Sanitize module name - only allow alphanumeric characters and spaces
        string moduleName = SanitizeFileName(filter.ModuleType.Trim());
        if (string.IsNullOrWhiteSpace(moduleName))
        {
            return BadRequest("Invalid module name");
        }

        string fileName = $"{moduleName}_Import";
        string finalPath = fileName.GetFinalPath();
        int companyId = 0;
        if (!string.IsNullOrEmpty(filter?.EncryptedPortfolioCompanyID))
            companyId = int.TryParse(_encryption.Decrypt(filter?.EncryptedPortfolioCompanyID), out var id) ? id : 0;
        int userId = _helperService.GetCurrentUserId(User);

        string pageConfigName = moduleName switch
        {
            Constants.MCapTable => await _configHeaderService.CreateCapTableExcelTemplate(finalPath, companyId, userId),
            Constants.MOtherCapTable => await _configHeaderService.CreateCapTableExcelTemplate(finalPath, companyId, userId,true),
            Constants.KpiFinancials => await _configHeaderService.CreateFinancialsExcelTemplate(finalPath, companyId, userId),
            Constants.FundFinancialsStr => await _configHeaderService.CreateFundFinancialsAndKpisExcelTemplate(finalPath, filter.FundId, userId, moduleName, filter.FundName),
            Constants.FundKPIs => await _configHeaderService.CreateFundFinancialsAndKpisExcelTemplate(finalPath, filter.FundId, userId, moduleName, filter.FundName),
            _ => await _configHeaderService.CreateKpiExcelHeaders(moduleName, finalPath, companyId)
        };

        if (!string.IsNullOrEmpty(pageConfigName))
        {
            fileName = $"{pageConfigName}_Import";
        }

        return new ExcelContentResult(SanitizeFileName(fileName), finalPath.ReplacePathDots());
    }

    private static string SanitizeFileName(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            return string.Empty;

        // Remove any characters that aren't alphanumeric, space, or underscore
        string sanitized = Regex.Replace(fileName, @"[^a-zA-Z0-9\s_]", "", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        
        // Ensure the filename doesn't start with a dot or space
        sanitized = sanitized.TrimStart('.', ' ');
        
        return string.IsNullOrWhiteSpace(sanitized) ? "unnamed" : sanitized;
    }
}
