using System;
using System.Collections.Generic;
using System.Linq;
using ClosedXML.Excel;
using Contract.CapTable;
using Contract.PortfolioCompany;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models.PageSettings;
using Shared;
using Utility.Helpers;
using static Exports.Services.ConfigHeaderService;

namespace Exports.Helpers
{
    public static class KpiHeaderHelper
    {
        /// <summary>
        /// Creates Excel headers in the specified worksheet.
        /// </summary>
        /// <param name="worksheet">The worksheet where the headers will be created.</param>
        /// <param name="headers">A list of strings representing the headers to be created.</param>
        /// <remarks>
        /// This method initializes the column index and row index to 1.
        /// It then iterates over the list of headers, and for each header, it calls the CreateExcelHeaders method of the ExcelFileHelper class to create the header in the worksheet.
        /// After creating a header, it increments the column index by 1.
        /// </remarks>
        public static void CreateHeaders(IXLWorksheet worksheet, List<string> headers, int rowIndex = 1, int columnIndex = 1)
        {
            headers.ForEach(item =>
            {
                ExcelFileHelper.CreateExcelHeaders(worksheet, rowIndex, columnIndex, item);
                columnIndex++;
            });
        }
        /// <summary>
        /// Fills the specified worksheet with data from a list of KPI templates.
        /// </summary>
        /// <param name="worksheet">The worksheet where the data will be filled.</param>
        /// <param name="mappedKpiList">A list of KpiTemplate objects representing the data to be filled.</param>
        /// <remarks>
        /// This method initializes the column index to 1 and the row index to 2.
        /// It then iterates over the list of KPI templates, and for each template, it does the following:
        /// - Sets the value of the cell at the current row and column index to the LineItem of the template and sets its data type to Text.
        /// - Sets the value of the cell at the current row and column 2 to the Id of the template and sets its data type to Number.
        /// - Sets the font of the cell at the current row and column index to bold if the template is a header.
        /// After processing a template, it increments the row index by 1.
        /// </remarks>
        public static void FillData(IXLWorksheet worksheet, List<KpiTemplate> mappedKpiList, int rowIndex = 2)
        {
            int columnIndex = 1;
            mappedKpiList.ForEach(item =>
            {
                var cell1 = worksheet.Cell(rowIndex, columnIndex);
                cell1.SetValue(item.LineItem);
                cell1.DataType = XLDataType.Text;
                cell1.Style.Font.Bold = item.IsHeader;
                cell1.Style.Font.FontSize = Constants.CellValueFontSize;
                cell1.Style.Alignment.WrapText = true;
                var cell2 = worksheet.Cell(rowIndex, 2);
                cell2.SetValue(item.Id);
                cell2.DataType = XLDataType.Number;
                rowIndex++;
            });
        }
        /// <summary>
        /// Fills the column data in the specified worksheet with the given list of KpiTemplate objects.
        /// </summary>
        /// <param name="worksheet">The worksheet to fill with column data.</param>
        /// <param name="mappedKpiList">The list of KpiTemplate objects containing the data to fill the columns.</param>
        /// <returns>The index of the next column after filling the data.</returns>
        public static int FillColumnData(IXLWorksheet worksheet, List<KpiTemplate> mappedKpiList)
        {
            int columnIndex = 3;
            mappedKpiList?.ForEach(item =>
            {
                var cell1 = worksheet.Cell(5, columnIndex);
                cell1.SetValue(item.LineItem);
                worksheet.Column(columnIndex).Width = 40; // Set the width of the column
                cell1.DataType = XLDataType.Text;
                cell1.Style.Font.Bold = true;
                cell1.Style.Fill.BackgroundColor = XLColor.FromHtml(Constants.HeaderColorCode);
                cell1.Style.Font.FontSize = Constants.CellHeaderFontSize;
                cell1.Style.Font.FontColor = XLColor.FromHtml(Constants.WhiteColor);
                var cell2 = worksheet.Cell(3, columnIndex);
                cell2.SetValue(item.Id);
                cell2.DataType = XLDataType.Number;
                columnIndex++;
            });
            return columnIndex;
        }
        /// <summary>
        /// Sets the quarterly value in the specified worksheet and row.
        /// </summary>
        /// <param name="ws">The worksheet to set the value in.</param>
        /// <param name="row">The row number to set the value in.</param>
        public static void SetQuarterlyValue(IXLWorksheet ws, int row)
        {
            ws.Cell(row, 1).Value = Constants.Quarter;
            GetQuarter(out int quarterNumber, out int quarterYear);
            ws.Cell(row, 2).SetValue<string>($"Q{quarterNumber}-{quarterYear}");
        }

        /// <summary>
        /// Sets the annual value in the specified worksheet and row.
        /// </summary>
        /// <param name="ws">The worksheet to set the value in.</param>
        /// <param name="row">The row number to set the value in.</param>
        public static void SetAnnualValue(IXLWorksheet ws, int row)
        {
            ws.Cell(row, 1).Value = Constants.Annual;
            ws.Cell(row, 2).SetValue<int>(DateTime.Now.AddMonths(-1).Year - 1);
        }

        /// <summary>
        /// Sets the half-annual value in the specified worksheet and row.
        /// </summary>
        /// <param name="ws">The worksheet to set the value in.</param>
        /// <param name="row">The row number to set the value in.</param>
        public static void SetHalfAnnualValue(IXLWorksheet ws, int row)
        {
            ws.Cell(row, 1).Value = Constants.Half_Annual;
            GetHalfYear(out int halfYearNumber, out int halfYearYear);
            ws.Cell(row, 2).SetValue<string>($"H{halfYearNumber}-{halfYearYear}");
        }

        // Extracted method for setting cell styles
        /// <summary>
        /// Sets the cell styles for a given worksheet and row.
        /// </summary>
        /// <param name="ws">The worksheet to set the cell styles on.</param>
        /// <param name="row">The row number to apply the cell styles to.</param>
        public static void SetCellStyles(IXLWorksheet ws, int row)
        {
            ws.Cell(row, 1).Style.Font.FontSize = Constants.CellHeaderFontSize;
            ws.Cell(row, 2).Style.Font.FontSize = Constants.CellHeaderFontSize;
        }
        /// <summary>
        /// Sets the period value in the specified worksheet based on the given period type.
        /// </summary>
        /// <param name="worksheet">The worksheet to set the period value in.</param>
        /// <param name="periodType">The type of period to set. Default value is Monthly.</param>
        public static void SetPeriod(IXLWorksheet worksheet, string periodType = Constants.Monthly)
        {
            DateTime currentDateTime = DateTime.Now;
            DateTime previousMonth = currentDateTime.AddMonths(-1);
            string cellValue = string.Empty;
            switch (periodType)
            {
                case Constants.Monthly:
                    cellValue = previousMonth.ToString("MMM-yyyy");
                    break;
                case Constants.Quarterly:
                    GetQuarter(out int quarterNumber, out int quarterYear);
                    cellValue = $"Q{quarterNumber}-{quarterYear}";
                    break;
                case Constants.Annual:
                    cellValue = previousMonth.Year.ToString();
                    break;
                case Constants.Half_Annual:
                    GetHalfYear(out int halfYearNumber, out int halfYearYear);
                    cellValue = $"H{halfYearNumber}-{halfYearYear}";
                    break;
            }
            worksheet.Cell(2, 2).SetValue<string>(cellValue);
        }

        /// <summary>
        /// Calculates the quarter number and year based on the current date.
        /// </summary>
        /// <param name="quarterNumber">The calculated quarter number.</param>
        /// <param name="quarterYear">The calculated quarter year.</param>
        public static void GetQuarter(out int quarterNumber, out int quarterYear)
        {
            quarterYear = DateTime.Now.AddMonths(-1).Year;
            quarterNumber = Common.GetQuarterFromMonthNumber(DateTime.Now.AddMonths(-1).Month, 12);
        }

        /// <summary>
        /// Calculates the half-year number and year based on the current date.
        /// </summary>
        /// <param name="halfYearNumber">The calculated half-year number (1 or 2).</param>
        /// <param name="halfYearYear">The calculated half-year year.</param>
        public static void GetHalfYear(out int halfYearNumber, out int halfYearYear)
        {
            halfYearYear = DateTime.Now.AddMonths(-1).Year;
            int month = DateTime.Now.AddMonths(-1).Month;
            halfYearNumber = month <= 6 ? 1 : 2;
        }
        // Extracted methods for setting values
        /// <summary>
        /// Sets the monthly value in the specified worksheet and row.
        /// </summary>
        /// <param name="ws">The worksheet to set the value in.</param>
        /// <param name="row">The row number to set the value in.</param>
        public static void SetMonthlyValue(IXLWorksheet ws, int row)
        {
            ws.Cell(row, 1).Value = Constants.Monthly;
            ws.Cell(row, 2).SetValue<string>(DateTime.Now.AddMonths(-1).ToString("MMM-yyyy"));
        }
        /// <summary>
        /// Gets the period based on the specified field name and quarterly flag.
        /// </summary>
        /// <param name="fieldName">The field name.</param>
        /// <param name="isQuarterly">A flag indicating whether the period is quarterly.</param>
        /// <returns>The period string.</returns>
        public static string GetPeriod(string fieldName, bool isQuarterly = false)
        {
            if (fieldName == "Period Type")
                return isQuarterly ? "Q" : string.Empty;
            return isQuarterly ? $"({fieldName}) Q" : $"({fieldName}) ";
        }
        /// <summary>
        /// Generates a list of Excel headers based on the given field name and header type.
        /// </summary>
        /// <param name="fieldName">The name of the field.</param>
        /// <param name="headerType">The type of the header, which can be "Monthly", "Quarterly", "Annual", or "Half-Annual".</param>
        /// <returns>A list of strings representing the Excel headers.</returns>
        /// <remarks>
        /// This method creates a new list of headers and then checks the header type.
        /// If the header type is "Monthly", it adds headers for the past 3 months.
        /// If the header type is "Quarterly", it adds headers for the past 2 quarters.
        /// If the header type is "Annual", it adds a header for the past year.
        /// If the header type is "Half-Annual", it adds headers for H1 and H2 of the previous year.
        /// The headers are formatted as "(fieldName) period", where period is the month, quarter, year, or half-year.
        /// </remarks>
        public static List<string> GetExcelHeaders(string fieldName, string headerType)
        {
            List<string> headers = new();
            switch (headerType)
            {
                case nameof(HeaderType.Monthly):
                    headers.AddRange(Enumerable.Range(0, 3)
                        .Select(m => GetPeriod(fieldName) + new DateTime(DateTime.Now.AddYears(-1).Year, 1, 1).AddMonths(m).ToString("MMM-yyyy"))
                        .ToList());
                    break;

                case nameof(HeaderType.Quarterly):
                    headers.AddRange(Enumerable.Range(0, 2)
                        .Select(q => GetPeriod(fieldName, true) + (q + 1).ToString() + "-" + DateTime.Now.AddYears(-1).Year)
                        .ToList());
                    break;

                case nameof(HeaderType.Annual):
                    headers.AddRange(Enumerable.Range(0, 1)
                        .Select(y => GetPeriod(fieldName) + (DateTime.Now.AddYears(-1).Year + y).ToString())
                        .ToList());
                    break;
                case Constants.Half_Annual:
                    headers.AddRange(Enumerable.Range(0, 2)
                        .Select(h => GetPeriod(fieldName) + $"H{h + 1}-{DateTime.Now.AddYears(-1).Year}")
                        .ToList());
                    break;
            }
            return headers;
        }
        /// <summary>
        /// Sets the header value and styles for the specified worksheet based on the provided data.
        /// </summary>
        /// <param name="compData">The portfolio company details.</param>
        /// <param name="worksheet">The worksheet to set the header value and styles for.</param>
        /// <param name="activeSheet">The active cap template module.</param>
        /// <param name="headers">The list of headers.</param>
        public static void SetHeaderValueAndStyles(IXLWorksheet worksheet,string companyName, string sheetName)
        {
            // Use period as needed
            worksheet.Cell(2, 2).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            worksheet.Cell(1, 1).Value = companyName;
            worksheet.Column(2).Hide();
            worksheet.Row(3).Hide();
            worksheet.SheetView.ZoomScale = 60;
            worksheet.Name = sheetName?[..Math.Min(sheetName.Length, 24)];
        }

        /// <summary>
        /// Sets the period value based on the given subSections, worksheet, and activeSheet.
        /// </summary>
        /// <param name="subSections">The list of MSubSectionFields.</param>
        /// <param name="worksheet">The IXLWorksheet object.</param>
        /// <param name="activeSheet">The CapTemplateModule object representing the active sheet.</param>
        public static void SetPeriodValue(List<MSubSectionFields> subSections, IXLWorksheet worksheet, CapTemplateModule activeSheet)
        {
            var periodTypes = subSections.Where(x => x.FieldID == activeSheet.FieldId)?.Select(x => x.ChartValue?.Split(",")?.ToList())?.SelectMany(x => x).Distinct()?.ToList();
            string[] periods = [Constants.Monthly, Constants.Quarterly, Constants.Annual, Constants.Half_Annual];
            foreach (var period in periods)
            {
                if (periodTypes.Contains(period))
                {
                    KpiHeaderHelper.SetPeriod(worksheet, period);
                    break;
                }
            }
        }

        /// <summary>
        /// Post-processes the worksheet by removing inactive financials worksheets and saving the workbook.
        /// </summary>
        /// <param name="pageConfigActiveSheets">The list of active sheets.</param>
        /// <param name="xLWorkbook">The workbook to be processed.</param>
        public static void PostProcessWorkSheet(List<CapTemplateModule> pageConfigActiveSheets, XLWorkbook xLWorkbook)
        {
            var sheets = pageConfigActiveSheets.Select(x => x.AliasName?[..Math.Min(x.AliasName.Length, 24)]).ToList();
            sheets.Add(Constants.SheetReference);
            ExcelFileHelper.RemoveInactiveFinancialsWorksheet([.. xLWorkbook.Worksheets], sheets);
            xLWorkbook.Save();
        }
        public static void OrganizeSheets(XLWorkbook xLWorkbook, List<CapTemplateModule> sheetOrders)
        {
            foreach (var sheetOrder in sheetOrders)
            {
                var sheet = xLWorkbook.Worksheet(sheetOrder.Name);
                if (sheet != null)
                {
                    sheet.Position = (int)sheetOrder.Order;
                }
            }
        }
    }
}